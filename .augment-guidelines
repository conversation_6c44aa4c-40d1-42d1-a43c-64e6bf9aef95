- Don't write code without using it; ensure everything written is utilized in the project.
- Comments must explain 'what' and 'why', not 'how' - code should be self-explanatory in implementation. Specifically: 'what' refers to the business logic or purpose the code serves, 'why' explains the reasoning behind design decisions, trade-offs made, or non-obvious choices. The 'how' should be evident from clean, well-structured code with meaningful variable names and clear control flow.
- Avoid over-commenting - excessive comments indicate poor code quality. A well-written codebase needs minimal comments because the code itself communicates clearly. If you find yourself writing many comments to explain code, refactor the code instead. Comments should be rare and valuable, not cluttering every line.
- Function comments must explain purpose and reasoning, placed at function beginnings.
- Code must prioritize readability for human understanding over computer execution efficiency.
- Maintain long-term maintainability over short-term optimization.
- Understand and design proper data structures first - good data structures lead to good code.
- Avoid unnecessary complexity - implement simple solutions unless complexity is truly required.
- New code you write is garbage if it doesn't follow <PERSON><PERSON>' clean code principles - the father of Linux. These principles include: keep it simple and obvious, make code readable like good prose, avoid premature optimization, write code that clearly expresses intent, minimize abstraction layers, and never add functionality "just in case" - only implement what's needed now. Remember: good taste in code means knowing when to stop adding features and complexity.
- Before implementation, first use provided reading, searching, browsing, web, and other tools to understand the data structure of the request.
- Avoid over-engineering - focus on delivering the minimal viable solution that meets the specified acceptance criteria.
- Only create automated tests if explicitly required in the original requirements.
- Define all data input/output structures first before writing any logic.
- Define all function input parameters and return values before implementation.
- Define all required functions and their signatures at once before writing implementation logic.
- Implementation logic should be written only after all data structures and function definitions are complete.

- ALWAYS run `ruff check` and `ruff format` after done coding task.